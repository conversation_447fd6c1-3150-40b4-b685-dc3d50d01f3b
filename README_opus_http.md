# ESP32 Opus HTTP 流播放器

## 🎵 简单的边下载边播放实现

这是一个简化的Opus HTTP流播放器，专门适配简单的Python HTTP服务器。

## 📁 文件结构

```
main/audio/
├── sk_opus_http_stream.h    # 简洁接口
└── sk_opus_http_stream.c    # 简化实现
```

## 🚀 使用方法

### 1. 启动HTTP服务器

在您的电脑上（IP: ************）：

```bash
# 进入包含.opus文件的目录
cd /path/to/your/music/

# 启动简单HTTP服务器
python -m http.server 8080
```

### 2. 放置音频文件

确保目录中有 `music.opus` 文件：

```
your_music_directory/
├── music.opus
├── song1.opus
└── song2.opus
```

### 3. ESP32使用

- 编译并烧录固件
- 说 `"ying yue"` 开始播放
- 设备会从 `http://************:8080/music.opus` 下载并播放

## ⚙️ 工作原理

### 边下载边播放机制

1. **开始下载** → HTTP客户端连接到Python服务器
2. **数据缓冲** → 数据写入128KB环形缓冲区  
3. **达到阈值** → 缓冲64KB后立即开始播放
4. **并行处理** → 下载和播放同时进行
5. **自动结束** → 下载完成且缓冲区播放完毕后停止

### 核心配置

```c
#define MIN_BUFFER (64 * 1024)          // 64KB开始播放
#define RINGBUF_SIZE (128 * 1024)       // 128KB环形缓冲区
```

## 🔧 简化特性

- **极简HTTP配置**：适配 `python -m http.server`
- **ESP-IDF环形缓冲区**：自动内存管理
- **边下载边播放**：低延迟流媒体体验
- **错误处理**：基本的网络错误恢复

## 📝 接口说明

```c
// 初始化
int32_t SkOpusHttpInit(void);

// 播放URL
int32_t SkOpusHttpPlay(const char* url);

// 停止播放  
int32_t SkOpusHttpStop(void);

// 音频回调（内部使用）
size_t SkOpusHttpFeedAudio(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord);

// 清理
void SkOpusHttpDeinit(void);
```

## 🎯 优势

- **简单易用**：无需复杂的HTTP服务器配置
- **即点即播**：64KB缓冲后立即开始播放
- **内存高效**：使用ESP-IDF标准环形缓冲区
- **兼容性好**：适配标准Python HTTP服务器

## 🔍 调试信息

播放时会输出以下日志：

```
I OpusHttp: Downloading: http://************:8080/music.opus
I OpusHttp: Start playing (buffered: 64 KB)
I OpusHttp: Download completed: 1234567 bytes
```

## ⚠️ 注意事项

1. 确保ESP32和HTTP服务器在同一网络
2. 确保IP地址 `************` 正确
3. 音频文件必须是Opus格式
4. Python服务器默认端口是8000，代码中使用8080

## 🛠️ 故障排除

- **无法连接**：检查IP地址和端口
- **播放卡顿**：检查网络稳定性
- **无声音**：确认文件是Opus格式
- **内存不足**：检查PSRAM配置
