#!/usr/bin/env python3
"""
Opus文件测试和分析脚本
用于验证生成的Opus文件是否正确

使用方法：
python test_opus_file.py file.opus
"""

import sys
import os
import struct

def analyze_opus_file(file_path):
    """分析Opus文件格式"""
    
    if not os.path.exists(file_path):
        print(f"错误: 文件不存在: {file_path}")
        return False
    
    file_size = os.path.getsize(file_path)
    print(f"文件: {file_path}")
    print(f"大小: {file_size:,} 字节")
    
    with open(file_path, 'rb') as f:
        # 读取前几个字节
        header = f.read(32)
        
        print(f"\n前32字节 (hex): {header.hex()}")
        print(f"前32字节 (ascii): {header}")
        
        # 检查是否是OGG容器
        if header.startswith(b'OggS'):
            print("✅ 检测到OGG容器格式")
            analyze_ogg_format(f)
        else:
            print("✅ 检测到纯Opus流格式")
            analyze_raw_opus_format(f, header)
    
    return True

def analyze_ogg_format(f):
    """分析OGG容器格式"""
    f.seek(0)
    
    page_count = 0
    while True:
        # 寻找OGG页面
        pos = f.tell()
        data = f.read(4)
        if len(data) < 4:
            break
            
        if data == b'OggS':
            page_count += 1
            print(f"\n📄 OGG页面 {page_count} (位置: {pos})")
            
            # 读取页面头部
            f.seek(pos)
            header = f.read(27)
            if len(header) < 27:
                break
                
            # 解析头部
            version = header[4]
            header_type = header[5]
            granule_pos = struct.unpack('<Q', header[6:14])[0]
            serial = struct.unpack('<I', header[14:18])[0]
            page_seq = struct.unpack('<I', header[18:22])[0]
            checksum = struct.unpack('<I', header[22:26])[0]
            segments = header[26]
            
            print(f"  版本: {version}")
            print(f"  类型: {header_type}")
            print(f"  序列号: {serial}")
            print(f"  页面序号: {page_seq}")
            print(f"  段数: {segments}")
            
            # 读取段表
            segment_table = f.read(segments)
            if len(segment_table) < segments:
                break
                
            # 计算页面数据大小
            page_data_size = sum(segment_table)
            print(f"  数据大小: {page_data_size} 字节")
            
            # 读取页面数据的前几个字节
            page_data = f.read(min(16, page_data_size))
            if page_data.startswith(b'OpusHead'):
                print(f"  内容: OpusHead (头部信息)")
            elif page_data.startswith(b'OpusTags'):
                print(f"  内容: OpusTags (元数据)")
            else:
                print(f"  内容: 音频数据")
                print(f"  前16字节: {page_data.hex()}")
            
            # 跳到下一页
            f.seek(pos + 27 + segments + page_data_size)
        else:
            f.seek(pos + 1)

def analyze_raw_opus_format(f, header):
    """分析纯Opus流格式"""
    f.seek(0)
    
    packet_count = 0
    pos = 0
    
    print("\n🎵 Opus数据包分析:")
    
    while pos < 1000:  # 只分析前1000字节
        f.seek(pos)
        data = f.read(4)
        if len(data) < 4:
            break
            
        # 尝试解析Opus数据包
        if len(data) >= 1:
            toc = data[0]
            config = (toc >> 3) & 0x1F
            stereo = (toc >> 2) & 0x1
            frames = toc & 0x3
            
            packet_count += 1
            print(f"\n📦 数据包 {packet_count} (位置: {pos})")
            print(f"  TOC字节: 0x{toc:02x}")
            print(f"  配置: {config}")
            print(f"  立体声: {'是' if stereo else '否'}")
            print(f"  帧数: {frames + 1}")
            
            # 估算数据包大小（简化方法）
            if packet_count == 1:
                # 第一个包，尝试找到合理的大小
                packet_size = 160  # 典型的60ms@16kHz包大小
            else:
                packet_size = 160
                
            pos += packet_size
        else:
            break

def create_test_http_server():
    """创建简单的HTTP服务器脚本"""
    server_script = """#!/usr/bin/env python3
import http.server
import socketserver
import os

PORT = 8070

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        super().end_headers()

    def do_GET(self):
        print(f"请求: {self.path}")
        super().do_GET()

if __name__ == "__main__":
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"HTTP服务器启动在端口 {PORT}")
        print(f"访问: http://localhost:{PORT}/")
        print("按 Ctrl+C 停止服务器")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\\n服务器已停止")
"""
    
    with open("http_server.py", "w", encoding="utf-8") as f:
        f.write(server_script)
    
    print("✅ 已创建 http_server.py")
    print("使用方法: python http_server.py")

def main():
    if len(sys.argv) < 2:
        print("用法: python test_opus_file.py file.opus")
        print("或者: python test_opus_file.py --create-server")
        sys.exit(1)
    
    if sys.argv[1] == "--create-server":
        create_test_http_server()
        return
    
    file_path = sys.argv[1]
    analyze_opus_file(file_path)

if __name__ == "__main__":
    main()
