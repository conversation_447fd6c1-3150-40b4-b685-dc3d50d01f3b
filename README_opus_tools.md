# WAV to Opus 转换工具集

这是一套专为ESP32音频播放项目设计的WAV到Opus转换工具。

## 🎯 解决的问题

ESP32项目中遇到的音频播放问题：
- OGG容器格式复杂，需要解析头部和页面结构
- 现有的`.opus`文件实际是OGG容器，包含元数据页面
- 需要纯Opus流格式，避免容器解析的复杂性

## 📁 文件说明

### 1. `simple_wav_to_opus.py` - 简化转换工具（推荐）
最简单的转换工具，专门为ESP32优化：
```bash
python simple_wav_to_opus.py input.wav output.opus
```

**特点：**
- 输出纯Opus流（无OGG容器）
- 16kHz单声道，64kbps
- 60ms帧长度
- 针对语音优化

### 2. `wav_to_opus.py` - 完整转换工具
功能完整的转换工具，支持更多选项：
```bash
# 生成纯Opus流（推荐用于ESP32）
python wav_to_opus.py input.wav output.opus --raw

# 生成OGG容器格式
python wav_to_opus.py input.wav output.opus

# 自定义参数
python wav_to_opus.py input.wav output.opus --raw --bitrate 32000 --sample-rate 8000

# 分析音频文件
python wav_to_opus.py --analyze input.wav
```

### 3. `test_opus_file.py` - 文件分析工具
用于分析和验证生成的Opus文件：
```bash
# 分析Opus文件格式
python test_opus_file.py output.opus

# 创建HTTP测试服务器
python test_opus_file.py --create-server
```

## 🛠️ 安装依赖

### 系统依赖
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# macOS
brew install ffmpeg

# Windows
# 下载ffmpeg并添加到PATH
```

### Python依赖
```bash
# 仅简化工具需要（推荐）
# 无需额外依赖，只需ffmpeg

# 完整工具需要
pip install pydub
```

## 🎵 使用流程

### 1. 准备WAV文件
确保有一个WAV格式的音频文件，例如 `music.wav`

### 2. 转换为纯Opus流
```bash
python simple_wav_to_opus.py music.wav music.opus
```

### 3. 验证生成的文件
```bash
python test_opus_file.py music.opus
```

应该看到类似输出：
```
文件: music.opus
大小: 123,456 字节
✅ 检测到纯Opus流格式
```

### 4. 启动HTTP服务器测试
```bash
# 创建HTTP服务器脚本
python test_opus_file.py --create-server

# 启动服务器
python http_server.py
```

### 5. 在ESP32中测试
将生成的 `music.opus` 文件放在HTTP服务器目录中，然后在ESP32中播放：
```
http://************:8070/music.opus
```

## 🔧 ESP32代码修改建议

由于生成的是纯Opus流，可以简化ESP32代码：

### 1. 移除OGG头部跳过
```c
// 不再需要skip_ogg_headers函数
// 直接处理Opus数据
```

### 2. 简化数据处理
```c
// 直接将HTTP数据送入解码器，无需添加4字节头部
int32_t ret = SkOpusDecPlayRemote(SkOpusDecGetHandler(), 0, opus_data, read_len, &record);
```

## 📊 格式对比

| 格式 | 容器 | 头部 | ESP32兼容性 | 推荐度 |
|------|------|------|-------------|--------|
| `.opus` (OGG) | OGG | OpusHead + OpusTags | 需要解析 | ❌ |
| 纯Opus流 | 无 | 无 | 直接兼容 | ✅ |

## 🎯 参数说明

### 音频参数
- **采样率**: 16kHz（适合语音，文件小）
- **声道数**: 1（单声道，节省空间）
- **比特率**: 64kbps（质量与大小平衡）
- **帧长度**: 60ms（适合实时播放）

### 为什么选择这些参数？
1. **16kHz**: 语音质量足够，文件大小合理
2. **单声道**: ESP32通常用于语音应用
3. **64kbps**: 在质量和大小间取得平衡
4. **60ms帧**: 与ESP32的缓冲策略匹配

## 🚀 快速开始

1. **下载工具**：
   ```bash
   # 下载所有脚本到项目目录
   ```

2. **转换音频**：
   ```bash
   python simple_wav_to_opus.py your_music.wav music.opus
   ```

3. **验证文件**：
   ```bash
   python test_opus_file.py music.opus
   ```

4. **启动服务器**：
   ```bash
   python test_opus_file.py --create-server
   python http_server.py
   ```

5. **测试ESP32播放**：
   - 确保ESP32和电脑在同一网络
   - 修改ESP32代码中的服务器地址
   - 说"ying yue"测试播放

## 💡 故障排除

### 转换失败
- 检查ffmpeg是否正确安装
- 确认输入文件是有效的WAV格式

### ESP32播放无声音
- 确认生成的是纯Opus流格式
- 检查网络连接和HTTP服务器
- 验证ESP32的音频输出配置

### 文件过大
- 降低比特率：`--bitrate 32000`
- 降低采样率：`--sample-rate 8000`
- 使用更短的音频文件

这套工具应该能帮助您解决ESP32音频播放的格式兼容性问题！
