# WAV to OPUS Converter

This tool converts WAV audio files to OPUS format using the pyopus library.

## Installation

Before using the converter, you need to install the required dependencies:

```bash
pip install pyopus
```

Note: The `wave` module is part of Python's standard library, so it should be available by default.

## Usage

### Single File Conversion

To convert a single WAV file to OPUS:

```bash
python wav_to_opus_converter.py input.wav
```

This will create `input.opus` in the same directory.

To specify a different output file:

```bash
python wav_to_opus_converter.py input.wav -o output.opus
```

### Batch Conversion

To convert all WAV files in a directory:

```bash
python wav_to_opus_converter.py /path/to/wav/files/ -o /path/to/output/directory/
```

If no output directory is specified, files will be saved to `opus_output` directory.

### Bitrate Control

You can specify the bitrate for OPUS encoding (default is 64000):

```bash
python wav_to_opus_converter.py input.wav -b 96000
```

## Supported Formats

- Input: WAV files (8-bit, 16-bit, or 32-bit)
- Output: OPUS format
- Supported sample rates: Any standard sample rate (8000, 16000, 24000, 48000 Hz, etc.)
- Supported channels: Mono or stereo

## Example Usage

```bash
# Convert a single file
python wav_to_opus_converter.py audio.wav

# Convert with specific bitrate
python wav_to_opus_converter.py audio.wav -b 128000

# Batch convert all WAV files in a directory
python wav_to_opus_converter.py ./audio_files/ -o ./opus_files/
```

## Troubleshooting

If you encounter issues:

1. Make sure pyopus is installed: `pip install pyopus`
2. Ensure input files are valid WAV format
3. Check that you have write permissions to the output directory

## License

This tool is provided as part of the sk-terminal project.
