#!/usr/bin/env python3
"""
WAV to OPUS Converter
Converts WAV audio files to OPUS format using pyopus library
"""

import os
import sys
import argparse
from pathlib import Path

try:
    import pyopus
    import wave
    import struct
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install required libraries:")
    print("pip install pyopus wave")
    sys.exit(1)

def wav_to_opus(input_file, output_file, bitrate=64000):
    """
    Convert WAV file to OPUS format
    
    Args:
        input_file (str): Path to input WAV file
        output_file (str): Path to output OPUS file
        bitrate (int): Bitrate for OPUS encoding (default: 64000)
    """
    try:
        # Check if input file exists
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"Input file not found: {input_file}")
        
        # Open WAV file
        with wave.open(input_file, 'rb') as wav_file:
            # Get audio parameters
            channels = wav_file.getnchannels()
            sample_rate = wav_file.getframerate()
            sample_width = wav_file.getsampwidth()
            frames = wav_file.getnframes()
            
            print(f"Audio Info:")
            print(f"  Channels: {channels}")
            print(f"  Sample Rate: {sample_rate} Hz")
            print(f"  Sample Width: {sample_width} bytes")
            print(f"  Frames: {frames}")
            
            # Read audio data
            audio_data = wav_file.readframes(frames)
            
        # Initialize OPUS encoder
        encoder = pyopus.Encoder(sample_rate, channels, pyopus.APPLICATION_AUDIO)
        encoder.bitrate = bitrate
        
        # Convert audio data to the right format (16-bit)
        if sample_width == 1:
            # 8-bit to 16-bit conversion
            audio_data = struct.pack('<' + 'h' * len(audio_data), 
                                   *[x * 256 for x in audio_data])
        elif sample_width == 2:
            # Already 16-bit, no conversion needed
            pass
        elif sample_width == 4:
            # 32-bit to 16-bit conversion
            audio_data = struct.pack('<' + 'h' * (len(audio_data) // 2), 
                                   *[x // 65536 for x in struct.unpack('<' + 'i' * (len(audio_data) // 4), audio_data)])
        else:
            raise ValueError(f"Unsupported sample width: {sample_width}")
        
        # Encode audio data
        frame_size = 960  # Standard frame size for OPUS
        encoded_data = bytearray()
        
        # Process audio in chunks
        for i in range(0, len(audio_data) // (2 * channels), frame_size):
            # Extract a frame of audio data
            start = i * 2 * channels
            end = min((i + frame_size) * 2 * channels, len(audio_data))
            frame = audio_data[start:end]
            
            # Pad frame if necessary
            if len(frame) < frame_size * 2 * channels:
                frame += b'\x00' * (frame_size * 2 * channels - len(frame))
            
            # Encode frame
            encoded_frame = encoder.encode(frame, frame_size)
            encoded_data.extend(encoded_frame)
        
        # Write encoded data to output file
        with open(output_file, 'wb') as f:
            f.write(encoded_data)
        
        print(f"Successfully converted {input_file} to {output_file}")
        print(f"Output file size: {len(encoded_data)} bytes")
        
    except Exception as e:
        print(f"Error converting file: {e}")
        sys.exit(1)

def batch_convert(input_dir, output_dir, bitrate=64000):
    """
    Convert all WAV files in a directory to OPUS format
    
    Args:
        input_dir (str): Directory containing WAV files
        output_dir (str): Directory to save OPUS files
        bitrate (int): Bitrate for OPUS encoding
    """
    # Create output directory if it doesn't exist
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Find all WAV files in input directory
    wav_files = list(Path(input_dir).glob("*.wav"))
    
    if not wav_files:
        print(f"No WAV files found in {input_dir}")
        return
    
    print(f"Found {len(wav_files)} WAV files to convert")
    
    # Convert each file
    for wav_file in wav_files:
        output_file = Path(output_dir) / f"{wav_file.stem}.opus"
        print(f"Converting {wav_file.name}...")
        wav_to_opus(str(wav_file), str(output_file), bitrate)
    
    print(f"Batch conversion complete. Files saved to {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Convert WAV files to OPUS format")
    parser.add_argument("input", help="Input WAV file or directory")
    parser.add_argument("-o", "--output", help="Output OPUS file or directory")
    parser.add_argument("-b", "--bitrate", type=int, default=64000, 
                        help="Bitrate for OPUS encoding (default: 64000)")
    
    args = parser.parse_args()
    
    # Check if input is a file or directory
    if os.path.isfile(args.input):
        # Single file conversion
        if not args.output:
            # Generate output filename
            input_path = Path(args.input)
            args.output = input_path.with_suffix('.opus')
        
        wav_to_opus(args.input, args.output, args.bitrate)
        
    elif os.path.isdir(args.input):
        # Batch conversion
        if not args.output:
            args.output = "opus_output"
        
        batch_convert(args.input, args.output, args.bitrate)
        
    else:
        print(f"Error: {args.input} is not a valid file or directory")
        sys.exit(1)

if __name__ == "__main__":
    main()
