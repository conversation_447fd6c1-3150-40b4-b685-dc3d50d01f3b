/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_music.c
 * @description: 音乐和故事播放子状态.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include "sk_log.h"
#include "sk_common.h"
#include "sk_audio.h"
#include "sk_sm.h"
#include "sk_rlink.h"
#include "sk_opus_dec.h"
#include "sk_opus_http_stream.h"
#include "sm.h"

#define TAG "SmMusic"

typedef struct {
    SkStateHandler handler;
    SkSmStateEndCallback endCb;
} SmMusicCtrl;

SmMusicCtrl g_musicSmCtrl;

int32_t SmMusicStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SK_LOGI(TAG, "Start music (HTTP Opus mode)");

    SkPlayerResume();
    SkRecorderResume();

    // 使用HTTP Opus流播放器
    SkPlayerSetCallback(SkOpusHttpFeedAudio);
    int32_t ret = SkOpusHttpPlay("http://************:8070/Desktop/music.opus");
    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "HTTP Opus stream started");
    } else {
        SK_LOGE(TAG, "Failed to start HTTP Opus stream, fallback to WebSocket");
        // 失败时回退到WebSocket模式
        SkPlayerSetCallback(SkOpusDecFeedPlayAudio);
        SkRlinkSendMusicCommand("start", "music");
    }

    return SK_RET_SUCCESS;
}

int32_t SmMusicStop(SkSubStateInfo *info) {
    SK_LOGE(TAG, "Stop chat");
    return SK_RET_SUCCESS;
}

int32_t SmMusicEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmMusicCtrl *ctrl = (SmMusicCtrl *)info->privateData;

    if (event->event == SM_EVENT_CMD) {
        switch (event->subEvent) {
            case SPEECH_CMD_EVENT_PAUSE:
                SkPlayerPause();
                SkRecorderPause();
                // 发送暂停命令给服务器
                SkRlinkSendMusicCommand("pause", NULL);
                SK_LOGI(TAG, "Sent music pause command to server");
                break;
            case SPEECH_CMD_EVENT_RESUME:
                SkPlayerResume();
                SkRecorderResume();
                // 发送恢复命令给服务器
                SkRlinkSendMusicCommand("resume", NULL);
                SK_LOGI(TAG, "Sent music resume command to server");
                break;
            case SPEECH_CMD_EVENT_NEXT:
                // 发送下一首命令给服务器
                SkRlinkSendMusicCommand("next", NULL);
                SK_LOGI(TAG, "Sent music next command to server");
                break;
            case SPEECH_CMD_EVENT_PREV:
                // 发送上一首命令给服务器
                SkRlinkSendMusicCommand("prev", NULL);
                SK_LOGI(TAG, "Sent music prev command to server");
                break;
            case SPEECH_CMD_EVENT_QUIT:
                // 发送停止命令给服务器
                SkRlinkSendMusicCommand("stop", NULL);
                SK_LOGI(TAG, "Sent music stop command to server");
                if (ctrl->endCb != NULL) {
                    ctrl->endCb(ctrl->handler, STATE_MUSIC);
                }
                break;
            default:
                SK_LOGE(TAG, "Unknown cmd %d", event->subEvent);
                return SK_RET_FAIL;
        }
    } else if (event->event == SM_EVENT_LINK) {
    }
    return SK_RET_SUCCESS;
}

int32_t SkSmMusicInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    g_musicSmCtrl.endCb = endCb;
    g_musicSmCtrl.handler = handler;
    item->info.subState = 0;
    item->info.privateData = &g_musicSmCtrl;
    item->startProc = SmMusicStart;
    item->stopProc = SmMusicStop;
    item->eventProc = SmMusicEventProc;
    return SK_RET_SUCCESS;
}
