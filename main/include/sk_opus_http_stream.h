/**
 * @file: sk_opus_http_stream.h
 * @description: 简化的Opus HTTP流播放器
 */
#ifndef SK_OPUS_HTTP_STREAM_H
#define SK_OPUS_HTTP_STREAM_H

#include "sk_common.h"
#include "sk_audio.h"

// 初始化和清理
int32_t SkOpusHttpInit(void);
void SkOpusHttpDeinit(void);

// 播放控制
int32_t SkOpusHttpPlay(const char* url);
int32_t SkOpusHttpStop(void);

// 音频回调（给SkPlayerSetCallback使用）
size_t SkOpusHttpFeedAudio(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord);

#endif
