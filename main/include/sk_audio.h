/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_audio.h
 * @description: 音频模块接口文件.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#ifndef __SK_AUDIO_H__
#define __SK_AUDIO_H__

#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include "sk_common.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CONFIG_VC_AFE_ENABLED 1

enum {
    TASK_RUN_DISABLE = 0,
    TASK_RUN_ENABLE = 1,
};

enum {
    REPEAT_MODE_ONCE = 0,
    REPEAT_MODE_SINGLE_LOOP = 1,
    REPEAT_MODE_LOOP = 2,
};

enum {
    PLAY_DATA_MODE_MUTE = 0,
    PLAY_DATA_MODE_AUDIO = 1,
    PLAY_DATA_MODE_NETWORK = 2,
    PLAY_DATA_MODE_MUSIC = 3,
    PLAY_DATA_MODE_NETWORK_END = 4,
};

typedef struct {
    int32_t commandId;
    char *command;
} SkSpeechMapItem;

typedef struct {
    char* name;
    const int16_t* data;
    int length;
    int sampleMode;
} SkAudioSrcItem;

typedef size_t (*SkFuncGetPlayData)(uint16_t *data, size_t len, SkAudioDownlinkTimeRecord *timeRecord);
typedef void (*SkVcAfeDataCallback)(uint16_t *data, size_t len, uint32_t dataMode, uint32_t tickCnt);
typedef void (*SkFuncSrCmdCallback)(int32_t commandId);

int32_t SkPlayerInit(int32_t bytesPerSample, int32_t samplePerChunk);
void SkPlayerDeinit();
void SkPlayerSetCallback(SkFuncGetPlayData func);
void SkPlayerPause();
void SkPlayerResume();
void SkPlayerSetPm(bool flag);
void SkPlayerEnableReport(bool flag);

uint32_t SkRecorderInit(int32_t bytesPerSample, int32_t samplePerChunk, int32_t channelNum);
void SkSrSetSendFunc(SkVcAfeDataCallback func);
void SkRecorderDeInit();
void SkRecorderPause();
void SkRecorderResume();
void SkRecorderStop();

void SkSrInit(int32_t samplePerChunk, int32_t recChanNum, int32_t inputBytesPerSample, int32_t srChanNum);
void SkSrRegister(SkSpeechMapItem *map, size_t len, SkFuncSrCmdCallback func);

void SkAudioInit(int32_t bytesPerSample, int32_t playerSamplePerChunk);
int32_t SkAudioGetChunkSize();
void SkSrShowStat();
void SkSrDataIn(int16_t *buffer, int sampleCnt);
void SkDataToEncode(uint16_t *buffer, int sampleCnt);
void SkVcFeedData(int16_t startPos);
void SkAudioProcessFeed(int16_t *buffer, int sampleCnt, int channelNum);
void SkVcProcessEnable(bool enable);
void SkSrProcessEnable(bool enable);

void SkSrSetPm(bool flag);
void SkRecorderSetPm(bool flag);
void SkAudioStartTasks();
void SkSrSetMnThd(uint8_t thd);

#ifdef __cplusplus
}
#endif

#endif