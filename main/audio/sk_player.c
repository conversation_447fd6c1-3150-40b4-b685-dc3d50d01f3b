/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_player.c
 * @description: 播放模块，播放PCM数据.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdbool.h>
#include <string.h>
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_audio.h"
#include "sk_board.h"
#include "sk_afe.h"
#include "sk_dfx.h"

#define TAG "SkPlayer"
#define MAX_MUSIC_CNT 8
#define MAX_AUDIO_CNT 8

#define CONFIG_SK_DEBUG_AUDIO_DELAY_RECORD      (1)

#define SAMPLE_MODE_8K 0
#define SAMPLE_MODE_16K 1

typedef struct {
    bool pmMode;
    int32_t taskFlag;
    size_t bufferSize;
    int16_t *buffer;
    int32_t bytesPerSample;
    int32_t samplePerChunk;
    int32_t pauseFlag;
    uint32_t playDataCnt;
    SkDfxCtrl_t dfxCtrl;
    SkFuncGetPlayData funcGetPlayData;
} SkPlayerCtrl;

SkPlayerCtrl g_playerCtrl;

void SkPlayerSetCallback(SkFuncGetPlayData func) {
     g_playerCtrl.funcGetPlayData = func;
}

void SkPlayerPause() {
    g_playerCtrl.pauseFlag = 1;
}

void SkPlayerResume() {
    g_playerCtrl.pauseFlag = 0;
}

size_t PlayerGetData(SkPlayerCtrl *ctrl, uint16_t *buff, size_t len) {
    size_t rcv_bytes = len;
    SkAudioDownlinkTimeRecord timeRecord;

    if (ctrl->funcGetPlayData != NULL) {
        rcv_bytes = ctrl->funcGetPlayData(buff, len, &timeRecord);
#if CONFIG_SK_DEBUG_AUDIO_DELAY_RECORD
        SkDfxRecordData(&ctrl->dfxCtrl, (void *)&timeRecord, sizeof(SkAudioDownlinkTimeRecord));
#endif
        ctrl->playDataCnt++;
    }

    if ((ctrl->pauseFlag == 1) || (ctrl->funcGetPlayData == NULL)) {
        memset(buff, 0, len);
        rcv_bytes = len;
    }

    return rcv_bytes;
}

void SkPlayerEnableReport(bool flag) {
#if CONFIG_SK_DEBUG_AUDIO_DELAY_RECORD
    if (flag) {
        SkDfxStart(&g_playerCtrl.dfxCtrl);
    } else {
        SkDfxStop(&g_playerCtrl.dfxCtrl);
    }
#endif
    return;
}

int32_t SkPlayerInit(int32_t bytesPerSample, int32_t samplePerChunk) {
    size_t bufferSize;
    SkPlayerCtrl *ctrl = &g_playerCtrl;
    int32_t *w_buf = NULL;

    if (bytesPerSample == sizeof(int16_t)) {
        bufferSize = samplePerChunk * sizeof(int16_t);
    } else {
        return SK_RET_FAIL;
    }

    w_buf = (int32_t *)malloc(bufferSize);
    if (w_buf == NULL) {
        SK_LOGI(TAG, "malloc failed!");
        return SK_RET_FAIL;
    }
    SK_LOGI(TAG, "player buffer %p size %d.", w_buf, bufferSize);
    ctrl->bytesPerSample = bytesPerSample;
    ctrl->buffer = (int16_t *)w_buf;
    ctrl->bufferSize = bufferSize;
    ctrl->samplePerChunk = samplePerChunk;
    ctrl->taskFlag = true;
    ctrl->pauseFlag = false;

#if CONFIG_SK_DEBUG_AUDIO_DELAY_RECORD
    SkDfxInit(&ctrl->dfxCtrl, MSG_DFX_AND_TERM_TIME_RECORD, 1024, sizeof(SkAudioDownlinkTimeRecord));
#endif

    return SK_RET_SUCCESS;
}

void SkPlayerDeinit() {
    SkPlayerCtrl *ctrl = &g_playerCtrl;
    if (ctrl->buffer != NULL) {
        free(ctrl->buffer);
    }
    ctrl->buffer = NULL;
    ctrl->bufferSize = 0;
}

void SkGenerateAudio(int16_t *data, size_t len) {
    for (int i = 0; i < len; i++) {
        data[i] = i * 8;
    }
}

void SkPlayerTask(void *args) {
    SkPlayerCtrl *ctrl = &g_playerCtrl;
    esp_err_t ret;
    size_t w_bytes;
    size_t rcv_bytes;

    w_bytes = ctrl->bufferSize;
    rcv_bytes = ctrl->bufferSize;
    /* (Optional) Preload the data before enabling the TX channel, so that the valid data can be transmitted immediately */
    while (w_bytes == rcv_bytes && rcv_bytes != 0) {
        rcv_bytes = PlayerGetData(ctrl, (uint16_t *)ctrl->buffer, ctrl->bufferSize);
        /* Here we load the target buffer repeatedly, until all the DMA buffers are preloaded */
        ret = SkBspPlayPreload((int16_t *)ctrl->buffer, rcv_bytes, &w_bytes);
        if (ret != ESP_OK) {
            SK_LOGI(TAG, "SkBspPlayPreload failed");
            SkPlayerDeinit();
            return;
        }
        SK_LOGI(TAG, "Write Task: SkBspPlayPreload %d bytes, recv %d bytes", w_bytes, rcv_bytes);
    }

    while (ctrl->taskFlag) {
        if (ctrl->pmMode) {
            vTaskDelay(100 / portTICK_PERIOD_MS);
            continue;
        }
        /* Enable the TX channel */
        SK_LOGI(TAG, "Write Task: start speaker");
        ESP_ERROR_CHECK(SkBspStartSpk());
        if (w_bytes < rcv_bytes) {
            if (SkBspPlayAudio((int16_t *)&ctrl->buffer[w_bytes/sizeof(uint32_t)], rcv_bytes-w_bytes, 1000) == ESP_OK) {
                SK_LOGI(TAG, "Write Task: i2s write %d bytes", w_bytes);
            }
        }

        while (!ctrl->pmMode) {
    #if CONFIG_SK_DEBUG_AUDIO_DELAY_RECORD
            SkDfxProcCall(&ctrl->dfxCtrl);
    #endif 
            rcv_bytes = PlayerGetData(ctrl, (uint16_t *)ctrl->buffer, ctrl->bufferSize);
            if (rcv_bytes == 0) {
                ESP_LOGD(TAG, "No audio data from callback");
                continue;
            }
            ESP_LOGD(TAG, "Got audio data: %d bytes", rcv_bytes);

            /* Write i2s data */
            if (SkBspPlayAudio((int16_t *)ctrl->buffer, rcv_bytes, portMAX_DELAY) == ESP_OK) {
                ESP_LOGD(TAG, "Write Task: i2s write %d bytes", rcv_bytes);
            } else {
                SK_LOGI(TAG, "Write Task: i2s write failed");
            }
        }
        SkBspStopSpk();
        SK_LOGI(TAG, "Write Task: stop speaker");
    }

    SK_LOGI(TAG, "Receive done");
    SK_OS_TASK_END();
}

void SkPlayerSetPm(bool flag) {
    SkPlayerCtrl *ctrl = &g_playerCtrl;

    ctrl->pmMode = flag;
#if CONFIG_SK_PM_SLEEP_MODE == CONFIG_SK_PM_SLEEP_MODE_LIGHT2
    ctrl->taskFlag = !flag;
#endif
    return;
}