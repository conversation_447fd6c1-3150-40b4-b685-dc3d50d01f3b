#!/usr/bin/env python3
"""
简化版WAV to Opus转换脚本
专门为ESP32项目生成纯Opus流

使用方法：
python simple_wav_to_opus.py input.wav output.opus

特点：
- 输出纯Opus流（无OGG容器）
- 针对ESP32优化的参数
- 16kHz单声道，适合语音播放
"""

import subprocess
import sys
import os

def convert_wav_to_raw_opus(input_file, output_file):
    """
    使用ffmpeg将WAV转换为纯Opus流
    参数针对ESP32优化
    """
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    # ESP32优化参数
    cmd = [
        "ffmpeg",
        "-i", input_file,           # 输入文件
        "-c:a", "libopus",          # 使用Opus编码器
        "-b:a", "64k",              # 64kbps比特率
        "-ar", "16000",             # 16kHz采样率
        "-ac", "1",                 # 单声道
        "-frame_duration", "60",    # 60ms帧长度
        "-application", "voip",     # 语音优化
        "-f", "opus",               # 输出纯Opus流
        "-y",                       # 覆盖输出文件
        output_file
    ]
    
    print(f"正在转换: {input_file} -> {output_file}")
    print("参数: 16kHz, 单声道, 64kbps, 60ms帧")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            input_size = os.path.getsize(input_file)
            output_size = os.path.getsize(output_file)
            print(f"✅ 转换成功!")
            print(f"输入: {input_size:,} 字节")
            print(f"输出: {output_size:,} 字节")
            print(f"压缩率: {(1-output_size/input_size)*100:.1f}%")
            return True
        else:
            print(f"❌ 转换失败:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("错误: 未找到ffmpeg")
        print("请安装ffmpeg:")
        print("  Ubuntu: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        return False
    except Exception as e:
        print(f"转换出错: {e}")
        return False

def main():
    if len(sys.argv) != 3:
        print("用法: python simple_wav_to_opus.py input.wav output.opus")
        print("示例: python simple_wav_to_opus.py music.wav music.opus")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    success = convert_wav_to_raw_opus(input_file, output_file)
    
    if success:
        print("\n🎵 生成的Opus文件可以直接用于ESP32播放!")
        print("💡 这是纯Opus流，无需解析OGG容器")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
